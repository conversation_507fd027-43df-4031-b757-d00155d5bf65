'use client';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useLanguage } from '../../contexts/LanguageContext';
import styles from './BrowseByMake.module.css';

// Default fallback logo for all brands
const DEFAULT_LOGO = '/car-logos/default-car-logo.svg';

// Car brand logo configuration - only logos that actually exist in public/car-logos/
const brandConfig = {
  'Toyota': { logo: '/car-logos/toyota-logo-2005.webp', fallback: '🚗' },
  'Ford': { logo: '/car-logos/ford-logo.webp', fallback: '🚐' },
  'Chevrolet': { logo: '/car-logos/chevrolet-logo.webp', fallback: '🚕' },
  'Nissan': { logo: '/car-logos/nissan-logo-2001-2000x1750-show.webp', fallback: '🚗' },
  'Hyundai': { logo: '/car-logos/hyundai-logo.webp', fallback: '🚙' },
  'Kia': { logo: '/car-logos/kia-logo.webp', fallback: '🚐' },
  'Mazda': { logo: '/car-logos/mazda-logo.webp', fallback: '🚕' },
  'Subaru': { logo: '/car-logos/subaru-logo.webp', fallback: '🚗' },
  'Volkswagen': { logo: '/car-logos/Volkswagen-logo-2015-1920x1080.webp', fallback: '🚙' },
  'BMW': { logo: '/car-logos/bmw-logo-1997.webp', fallback: '🚐' },
  'Mercedes-Benz': { logo: '/car-logos/mercedes-benz-logo.webp', fallback: '🚕' },
  'Audi': { logo: '/car-logos/audi-logo.webp', fallback: '🚗' },
  'Lexus': { logo: '/car-logos/lexus-logo.webp', fallback: '🚙' },
  'Acura': { logo: '/car-logos/acura-logo.webp', fallback: '🚐' },
  'Jeep': { logo: '/car-logos/jeep-logo-1993-640.webp', fallback: '🚗' },
  'Dodge': { logo: '/car-logos/dodge-logo.webp', fallback: '🚐' },
  'Mini': { logo: '/car-logos/mini-logo.webp', fallback: '🚗' },
  'Suzuki': { logo: '/car-logos/suzuki-logo.webp', fallback: '🚙' },
  'Volvo': { logo: '/car-logos/volvo-logo.webp', fallback: '🚐' }
};

// Fallback for brands without logos
const getDefaultFallback = (make) => {
  const fallbacks = ['🚗', '🚙', '🚐', '🚕', '🚖'];
  const index = make.length % fallbacks.length;
  return fallbacks[index];
};

export default function BrowseByMake() {
  const [makes, setMakes] = useState([]);
  const [loading, setLoading] = useState(true);
  const router = useRouter();
  const { t } = useLanguage();

  useEffect(() => {
    const fetchMakes = async () => {
      try {
        const response = await fetch('/api/vehicles?makes=true');
        const data = await response.json();
        if (data.success) {
          // Show all makes, prioritizing those with logos, limit to 7 for display
          const makesWithLogos = data.data.filter(item => brandConfig[item.make]);
          const makesWithoutLogos = data.data.filter(item => !brandConfig[item.make]);
          const availableMakes = [...makesWithLogos, ...makesWithoutLogos].slice(0, 7);
          setMakes(availableMakes);
        }
      } catch (error) {
        console.error('Error fetching makes:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchMakes();
  }, []);

  const handleMakeClick = (make) => {
    router.push(`/inventory/used-cars?make=${encodeURIComponent(make)}`);
  };

  if (loading) {
    return (
      <section className={styles.browseSection}>
        <div className={styles.container}>
          <div className={styles.sectionHeader}>
            <h2 className={styles.sectionTitle}>
              {t('home.browse_by_make.title')} <span className={styles.makeHighlight}>{t('home.browse_by_make.make')}</span>
            </h2>
          </div>
          <div className={styles.loadingGrid}>
            {[...Array(7)].map((_, index) => (
              <div key={index} className={styles.loadingCard}>
                <div className={styles.loadingSkeleton}></div>
              </div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className={styles.browseSection}>
      <div className={styles.container}>
        <div className={styles.sectionHeader}>
          <h2 className={styles.sectionTitle}>
            {t('home.browse_by_make.title')} <span className={styles.makeHighlight}>{t('home.browse_by_make.make')}</span>
          </h2>
        </div>
        <div className={styles.brandsGrid}>
          {makes.map((item) => {
            const config = brandConfig[item.make];
            const hasConfiguredLogo = config && config.logo;
            const fallbackEmoji = config?.fallback || getDefaultFallback(item.make);

            // Always try to show a logo first (either configured or default)
            const logoToTry = hasConfiguredLogo ? config.logo : DEFAULT_LOGO;

            return (
              <div
                key={item.make}
                className={styles.brandCard}
              >
                <div
                  className={styles.brandLogo}
                  onClick={() => handleMakeClick(item.make)}
                  title={t('home.browse_by_make.view_all', { make: item.make })}
                >
                  {/* Always try to show a logo first */}
                  <img
                    src={logoToTry}
                    alt={`${item.make} logo`}
                    width={60}
                    height={60}
                    className={styles.logoImage}
                    loading="lazy"
                    onLoad={(e) => {
                      // Ensure image is visible when loaded successfully
                      e.target.style.display = 'block';
                      e.target.style.opacity = '1';
                      const fallbackElement = e.target.nextElementSibling;
                      if (fallbackElement) {
                        fallbackElement.style.display = 'none';
                      }
                    }}
                    onError={(e) => {
                      // Enhanced error handling with detailed logging
                      console.error(`❌ Failed to load logo for ${item.make}`);
                      console.error(`   Attempted URL: ${logoToTry}`);
                      console.error(`   Full resolved URL: ${e.target.src}`);

                      // Try fallback strategies in order
                      const currentSrc = e.target.src;
                      const alternativePath = `/car-logos/${item.make.toLowerCase()}-logo.webp`;
                      const alternativeUrl = window.location.origin + alternativePath;
                      const defaultUrl = window.location.origin + DEFAULT_LOGO;

                      // First try: alternative naming convention (only if we haven't tried it yet)
                      if (hasConfiguredLogo && currentSrc !== alternativeUrl && currentSrc !== defaultUrl) {
                        console.log(`🔄 Trying alternative path: ${alternativePath}`);
                        e.target.src = alternativePath;
                        return;
                      }

                      // Second try: default logo (only if we haven't tried it yet)
                      if (currentSrc !== defaultUrl) {
                        console.log(`🔄 Trying default logo: ${DEFAULT_LOGO}`);
                        e.target.src = DEFAULT_LOGO;
                        return;
                      }

                      // Final fallback: hide image and show emoji
                      console.log(`🎭 Using emoji fallback for ${item.make}`);
                      e.target.style.display = 'none';
                      const fallbackElement = e.target.nextElementSibling;
                      if (fallbackElement) {
                        fallbackElement.style.display = 'flex';
                        fallbackElement.style.opacity = '1';
                      }
                    }}
                    style={{
                      display: 'block',
                      opacity: '0',
                      transition: 'opacity 0.3s ease'
                    }}
                  />
                  <div
                    className={styles.brandEmoji}
                    style={{
                      display: 'none',
                      opacity: '0',
                      transition: 'opacity 0.3s ease'
                    }}
                    title={`${item.make} - Logo not available`}
                  >
                    {fallbackEmoji}
                  </div>
                </div>
                <div className={styles.brandName}>
                  {item.make}
                </div>
                <div className={styles.vehicleCount}>
                  {item.count} {item.count === 1 ? 'vehicle' : 'vehicles'}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
}
